/**
 * 订单表单验证规则
 */

/**
 * 获取订单表单验证规则
 * @param {Object} form - 表单数据对象的引用
 * @returns {Object} 验证规则对象
 */
export const getOrderFormRules = (form) => {
  return {
    // 订单编号已隐藏，不需要校验
    dealDate: {
      required: true,
      message: '请选择销售日期'
    },
    invoiceOrgName: {
      required: true,
      message: '请输入销售单位'
    },
    expectedOutboundDate: {
      required: true,
      message: '请选择预计出库日期'
    },
    outboundOrgName: {
      required: true,
      message: '请选择出库单位'
    },

    // 客户信息
    customerId: {
      required: true,
      message: '请选择客户'
    },
    customerName: {
      required: true,
      message: '请选择客户'
    },
    customerPhone: {
      required: true,
      message: '客户联系方式不能为空'
    },

    // 车辆信息
    skuId: {
      required: true,
      message: '请选择车辆'
    },

    // 金额信息
    finalPrice: {
      required: true,
      type: 'number',
      message: '成交总价必须大于零',
      validator: (_, value) => {
        if (value === null || value === undefined || value === '') {
          return new Error('成交总价不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('成交总价必须是数字')
        }
        if (value <= 0) {
          return new Error('成交总价必须大于零')
        }
        return true
      }
    },

    // 贷款信息 - 使用函数动态计算规则
    loanChannel: {
      required: () => form.paymentMethod === 'LOAN',
      message: '该字段为必选项'
    },
    loanAmount: {
      required: () => form.paymentMethod === 'LOAN',
      type: 'number',
      message: '该字段为必填项',
      validator: (_, value) => {
        if (form.paymentMethod !== 'LOAN') return true;

        if (value === null || value === undefined || value === '') {
          return new Error('分期金额不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('分期金额必须是数字')
        }
        if (value < 0) {
          return new Error('分期金额不能为负数')
        }
        if (value > form.finalPrice) {
          return new Error('分期金额不能大于成交总价')
        }
        // 检查贷款金额和首付金额之和是否等于成交总价
        const total = (value || 0) + (form.loanInitialAmount || 0)
        if (Math.abs(total - form.finalPrice) > 0.01) { // 允许0.01的误差
          return new Error('分期金额和首付金额之和必须等于成交金额')
        }
        return true
      }
    },
    loanInitialAmount: {
      required: () => form.paymentMethod === 'LOAN',
      type: 'number',
      message: '请输入首付金额',
      validator: (_, value) => {
        if (form.paymentMethod !== 'LOAN') return true;

        if (value === null || value === undefined || value === '') {
          return new Error('首付金额不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('首付金额必须是数字')
        }
        if (value < 0) {
          return new Error('首付金额不能为负数')
        }
        if (value > form.finalPrice) {
          return new Error('首付金额不能大于成交总价')
        }
        // 检查贷款金额和首付金额之和是否等于成交总价
        const total = (value || 0) + (form.loanAmount || 0)
        if (Math.abs(total - form.finalPrice) > 0.01) { // 允许0.01的误差
          return new Error('分期金额和首付金额之和必须等于成交总价')
        }
        return true
      }
    },
    loanMonths: {
      required: () => form.paymentMethod === 'LOAN',
      message: '请选择分期期数'
    },

    // 二手车信息 - 使用函数动态计算规则
    usedVehicleId: {
      required: () => form.hasUsedVehicle === 'YES',
      message: '请输入二手车车牌号'
    },
    usedVehicleVin: {
      required: () => form.hasUsedVehicle === 'YES' && form.usedVehicleId,
      message: '请输入二手车VIN码'
    },
    usedVehicleAmount: {
      required: () => form.hasUsedVehicle === 'YES' && form.usedVehicleId,
      type: 'number',
      message: '请输入置换金额',
      validator: (_, value) => {
        if (form.hasUsedVehicle !== 'YES' || !form.usedVehicleId) return true;

        if (value === null || value === undefined || value === '') {
          return new Error('置换金额不能为空')
        }
        if (typeof value !== 'number') {
          return new Error('置换金额必须是数字')
        }
        if (value <= 0) {
          return new Error('置换金额必须大于零')
        }
        return true
      }
    }
  }
}

export default getOrderFormRules

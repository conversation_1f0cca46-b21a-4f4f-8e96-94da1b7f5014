<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">付款方式</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">请选择客户的付款方式：</span>
      <n-radio-group v-model:value="form.paymentMethod">
        <n-radio-button value="FULL">全款</n-radio-button>
        <n-radio-button value="LOAN">分期</n-radio-button>
      </n-radio-group>
    </div>

    <!-- 贷款信息，仅在选择贷款时显示 -->
    <n-grid
      v-if="form.paymentMethod === 'LOAN'"
      :cols="5"
      :x-gap="16"
      :y-gap="1"
    >
      <n-grid-item>
        <n-form-item label="分期金融机构" path="loanChannel" required>
          <n-select
            v-model:value="form.loanChannel"
            :options="loanChannelOptions"
            placeholder="请选择分期金融机构"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="分期金额(元)" path="loanAmount" required>
          <n-input-number
            v-model:value="form.loanAmount"
            placeholder="请输入贷款金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleLoanAmountChange"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="首付金额(元)" path="loanInitialAmount" required>
          <n-input-number
            v-model:value="form.loanInitialAmount"
            placeholder="请输入首付金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleLoanInitialAmountChange"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="首付比例(%)" path="loanInitialRatio">
          <n-input-number
            v-model:value="form.loanInitialRatio"
            disabled
            style="width: 100%"
            :precision="2"
            :show-button="false"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="分期时长" path="loanMonths" required>
          <n-select
            v-model:value="form.loanMonths"
            :options="loanMonthsOptions"
            placeholder="请选择分期期数"
            @update:value="
              (val) => {
                form.loanMonths = val || 12;
              }
            "
          />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="分期服务费(元)" path="loanFee">
          <n-input-number
            v-model:value="form.loanFee"
            placeholder="请输入分期服务费"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleLoanFeeChange"
          />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item
          label="应收-机构-分期返利(元)"
          path="loanRebateReceivableAmount"
        >
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              v-model:value="form.loanRebateReceivableAmount"
              placeholder="应收机构分期返利"
              style="flex: 1"
              :precision="2"
              :min="0"
              button-placement="both"
            />
          </div>
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item
          label="应付-客户-分期返利(元)"
          path="loanRebatePayableAmount"
        >
          <n-input-number
            v-model:value="form.loanRebatePayableAmount"
            placeholder="应付客户分期返利"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
          />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="转车款金额(元)" path="loanRebateToVehicleAmount">
          <n-input-number
            v-model:value="form.loanRebateToVehicleAmount"
            placeholder="抵扣车款金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
          />
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import {
  NGrid,
  NGridItem,
  NFormItem,
  NInputNumber,
  NDivider,
  NRadioGroup,
  NRadioButton,
  NSelect,
  NCheckbox,
} from "naive-ui";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  loanChannelOptions: {
    type: Array,
    default: () => [],
  },
  loanMonthsOptions: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits([
  "handle-loan-amount-change",
  "handle-loan-initial-amount-change",
  "handle-loan-fee-change",
]);

// 处理贷款金额变化
const handleLoanAmountChange = () => {
  emit("handle-loan-amount-change");
};

// 处理首付金额变化
const handleLoanInitialAmountChange = () => {
  emit("handle-loan-initial-amount-change");
};

// 处理分期服务费变化
const handleLoanFeeChange = () => {
  emit("handle-loan-fee-change");
};
</script>




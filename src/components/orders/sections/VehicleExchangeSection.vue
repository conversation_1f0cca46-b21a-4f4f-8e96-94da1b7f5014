<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">车辆置换</span>
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <div class="option-row has-tip">
      <span class="option-label">客户是否有车辆置换？</span>
      <n-radio-group v-model:value="form.hasUsedVehicle">
        <n-radio-button value="NO">否</n-radio-button>
        <n-radio-button value="YES">是</n-radio-button>
      </n-radio-group>
      <span
        class="option-tip"
        v-if="form.hasUsedVehicle === 'YES'"
        v-tip-position
        style="color: #18a058 !important"
      >
        请填写置换车辆信息，车牌号和VIN码为必填项。
      </span>
    </div>

    <!-- 当选择"有"时显示二手车置换信息 -->
    <n-grid
      v-if="form.hasUsedVehicle === 'YES'"
      :cols="4"
      :x-gap="16"
      :y-gap="1"
    >
      <n-grid-item>
        <n-form-item label="置换车牌号" path="usedVehicleId" required>
          <n-input
            v-model:value="form.usedVehicleId"
            placeholder="请输入置换车牌号"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车VIN" path="usedVehicleVin" required>
          <n-input
            v-model:value="form.usedVehicleVin"
            placeholder="请输入17位车辆VIN"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车品牌" path="usedVehicleBound">
          <n-input
            v-model:value="form.usedVehicleBound"
            placeholder="请输入置换车品牌"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="置换车型" path="usedVehicleModel">
          <n-input
            v-model:value="form.usedVehicleModel"
            placeholder="请输入置换车型"
          />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="置换金额(元)" path="usedVehicleAmount">
          <n-input-number
            v-model:value="form.usedVehicleAmount"
            placeholder="请输入置换金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="转车款金额(元)" path="usedVehicleToVehicleAmount">
          <n-input-number
            v-model:value="form.usedVehicleToVehicleAmount"
            placeholder="抵扣车款金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item
          label="应收-厂家-置换补贴(元)"
          path="usedVehicleDiscountReceivableAmount"
        >
          <n-input-number
            v-model:value="form.usedVehicleDiscountReceivableAmount"
            placeholder="应收厂家置换补贴"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item
          label="应付-客户-置换补贴(元)"
          path="usedVehicleDiscountPayableAmount"
        >
          <n-input-number
            v-model:value="form.usedVehicleDiscountPayableAmount"
            placeholder="应付客户置换补贴"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
          />
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import {
  NGrid,
  NGridItem,
  NFormItem,
  NInput,
  NInputNumber,
  NDivider,
  NRadioGroup,
  NRadioButton,
  NCheckbox,
} from "naive-ui";

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
});
</script>




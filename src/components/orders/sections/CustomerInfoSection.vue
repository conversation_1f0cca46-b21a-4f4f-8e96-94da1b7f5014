<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">客户信息</span>
      <n-button type="primary" size="small" @click="$emit('show-customer-selector')" class="title-button">
        <template #icon>
          <n-icon>
            <component :is="PersonOutlineIcon" />
          </n-icon>
        </template>
        选择客户
      </n-button>
      <div style="flex: 1;"></div> <!-- 添加一个占位元素，将剩余空间推到右侧 -->
    </div>
    <n-divider class="section-divider" style="height: 2px; background-image: linear-gradient(to right, var(--primary-color, #18a058) 0%, rgba(24, 160, 88, 0.1) 100%); border: none;"></n-divider>

    <n-grid :cols="4" :x-gap="16" :y-gap="6">
      <n-grid-item>
        <n-form-item label="客户名称" path="customerName" required>
          <n-input v-model:value="form.customerName" placeholder="请选择客户" readonly />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="联系电话" path="customerPhone" required>
          <n-input v-model:value="form.customerPhone" placeholder="联系电话" readonly />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="客户地址" path="customerAddress">
          <n-input v-model:value="form.customerAddress" placeholder="客户地址" readonly />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="销售顾问" path="salesperson">
          <n-input v-model:value="form.salesperson" placeholder="销售顾问" readonly />
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="销售单位" path="invoiceOrgName" required>
          <n-input v-model:value="form.invoiceOrgName" placeholder="请输入销售单位" readonly />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="已付定金(元)" path="depositAmount">
          <div style="display: flex; align-items: center; width: 100%;">
            <n-input-number v-model:value="form.depositAmount" placeholder="请输入已付定金" style="flex: 1;"
              :precision="2" :min="0" button-placement="both" />
            <n-checkbox v-model:checked="form.depositDeductible" style="margin-left: 10px; white-space: nowrap; width: 80px;">转车款</n-checkbox>
          </div>
        </n-form-item>
      </n-grid-item>

      <n-grid-item>
        <n-form-item label="专享优惠类型" path="exclusiveDiscountType">
          <n-select v-model:value="form.exclusiveDiscountType" :options="exclusiveDiscountTypeOptions"
            placeholder="请选择专享优惠类型" clearable />
        </n-form-item>
      </n-grid-item>

      <n-grid-item v-if="showExclusiveDiscountAmount">
        <n-form-item label="专享优惠金额(元)" path="exclusiveDiscountAmount">
          <n-input-number v-model:value="form.exclusiveDiscountAmount" placeholder="请输入专享优惠金额"
            :precision="2" :min="0" button-placement="both" />
        </n-form-item>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup>
import { markRaw, ref, computed, onMounted } from 'vue'
import { NButton, NIcon, NGrid, NGridItem, NFormItem, NInput, NInputNumber, NCheckbox, NDivider, NSelect } from 'naive-ui'
import { PersonOutline } from '@vicons/ionicons5'
import { getDictOptions } from '@/api/dict'

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const PersonOutlineIcon = markRaw(PersonOutline)

// 专享优惠类型选项
const exclusiveDiscountTypeOptions = ref([])

// 获取专享优惠类型选项
const fetchExclusiveDiscountTypeOptions = async () => {
  try {
    const res = await getDictOptions('exclusive_discount_type')
    if (res && res.code === 200 && res.data) {
      exclusiveDiscountTypeOptions.value = res.data.map(item => ({
        label: item.option_label,
        value: item.option_value
      }))
    }
  } catch (error) {
    console.error('获取专享优惠类型选项失败:', error)
  }
}

// 组件挂载时获取字典数据
onMounted(() => {
  fetchExclusiveDiscountTypeOptions()
})

// 是否显示专享优惠金额字段
const showExclusiveDiscountAmount = computed(() => {
  return props.form.exclusiveDiscountType && props.form.exclusiveDiscountType !== 'NONE'
})

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})

// 定义组件事件
defineEmits(['show-customer-selector'])
</script>



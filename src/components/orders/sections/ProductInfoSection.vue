<template>
  <div class="section-container">
    <div class="section-title">
      <span class="title-text">产品信息</span>
      <n-button
        type="primary"
        size="small"
        @click="handleShowVehicleSelector"
        class="title-button"
      >
        <template #icon>
          <n-icon>
            <component :is="CarOutlineIcon" />
          </n-icon>
        </template>
        选择车辆
      </n-button>
      <div style="flex: 1"></div>
      <!-- 添加一个占位元素，将剩余空间推到右侧 -->
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <!-- 车辆信息表单 -->
    <n-grid :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item>
        <n-form-item label="销售日期" path="dealDate" required>
          <n-date-picker
            v-model:value="form.dealDate"
            type="date"
            clearable
            style="width: 100%"
            value-format="timestamp"
            @update:value="handleDateChange('dealDate', $event)"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="预计出库日期" path="expectedOutboundDate" required>
          <n-date-picker
            v-model:value="form.expectedOutboundDate"
            type="date"
            clearable
            style="width: 100%"
            value-format="timestamp"
            @update:value="handleDateChange('expectedOutboundDate', $event)"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="出库单位" path="outboundOrgName" required>
          <div
            class="outbound-org-selector"
            :class="{ disabled: !form.vehicleBrand }"
          >
            <n-input
              v-model:value="form.outboundOrgName"
              placeholder="请先选择车辆品牌"
              readonly
              @click="handleOutboundOrgClick"
            >
              <template #suffix>
                <n-button
                  quaternary
                  circle
                  @click.stop="handleOutboundOrgClick"
                  :disabled="!form.vehicleBrand"
                >
                  <template #icon>
                    <n-icon
                      class="add-icon"
                      :class="{ disabled: !form.vehicleBrand }"
                    >
                      <component :is="AddOutlineIcon" />
                    </n-icon>
                  </template>
                </n-button>
              </template>
            </n-input>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item></n-grid-item>

      <n-grid-item>
        <n-form-item label="品牌" path="vehicleBrand">
          <n-input
            v-model:value="form.vehicleBrand"
            placeholder="车辆品牌"
            readonly
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="车型" path="vehicleSeries">
          <n-input
            v-model:value="form.vehicleSeries"
            placeholder="车辆车型"
            readonly
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="配置" path="vehicleConfig">
          <n-input
            v-model:value="form.vehicleConfig"
            placeholder="车辆配置"
            readonly
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="颜色代码" path="vehicleColorCode">
          <n-input
            v-model:value="form.vehicleColorCode"
            placeholder="颜色代码"
            readonly
          />
        </n-form-item>
      </n-grid-item>

      <!-- 金额信息 -->
      <n-grid-item>
        <n-form-item label="启票价格(元)" path="vehicleSbPrice">
          <n-input-number
            v-model:value="form.vehicleSbPrice"
            readonly
            style="width: 100%"
            :precision="2"
            :show-button="false"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="销售限价(元)" path="vehicleSalePrice">
          <n-input-number
            v-model:value="form.vehicleSalePrice"
            placeholder="销售（下）限价"
            readonly
            style="width: 100%"
            :precision="2"
            :show-button="false"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="优惠金额(元)" path="discountAmount">
          <n-input-number
            v-model:value="form.discountAmount"
            placeholder="请输入优惠金额"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleDiscountChange"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="销售费用(元)" path="salesExpense">
          <n-input-number
            v-model:value="form.salesExpense"
            placeholder="请输入销售费用"
            style="width: 100%"
            :precision="2"
            :min="0"
            button-placement="both"
            @update:value="handleSalesExpenseChange"
          />
        </n-form-item>
      </n-grid-item>

      <!-- 第二行：成交总价、成交总价（大写）、预估毛利润、预估毛利率 -->
      <n-grid-item>
        <n-form-item label="成交价格(元)" path="finalPrice" required>
          <div style="display: flex; align-items: center; width: 100%">
            <n-input-number
              v-model:value="form.finalPrice"
              readonly
              style="flex: 1"
              :precision="2"
              :show-button="false"
            />
            <span
              class="price-in-wan"
              style="margin-left: 10px; white-space: nowrap; width: 80px"
              >{{ finalPriceInWan }}</span
            >
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="成交价格（大写）" path="finalPriceChinese">
          <n-input
            v-model:value="form.finalPriceChinese"
            readonly
            style="width: 100%"
            :autosize="{ minRows: 1, maxRows: 3 }"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="预估毛利润(元)" path="profitAmount">
          <n-input-number
            v-model:value="form.profitAmount"
            readonly
            style="width: 100%"
            :precision="2"
            :show-button="false"
            :status="
              form.profitAmount < 0
                ? 'error'
                : form.profitAmount > 0
                ? 'success'
                : 'warning'
            "
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <n-form-item label="预估毛利率(%)" path="profitRate">
          <n-input-number
            v-model:value="form.profitRate"
            readonly
            style="width: 100%"
            :precision="2"
            :show-button="false"
            :status="
              form.profitRate > 20
                ? 'success'
                : form.profitRate > 0
                ? 'warning'
                : 'error'
            "
          />
        </n-form-item>
      </n-grid-item>
    </n-grid>

    <!-- 业务机构选择器 -->
    <BizOrgSelector
      :visible="showBizOrgSelector"
      @update:visible="showBizOrgSelector = $event"
      :business-permission="'can_stock_out'"
      :initial-biz-org="selectedOutboundOrg"
      :single="true"
      :brand="form.vehicleBrand"
      @select="handleOutboundOrgSelect"
      @cancel="handleOutboundOrgCancel"
    />
  </div>
</template>

<script setup>
import { computed, markRaw, ref } from "vue";
import { CarOutline, SearchOutline, AddOutline } from "@vicons/ionicons5";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { convertToWanYuan } from "@/utils/money";
import messages from "@/utils/messages";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const CarOutlineIcon = markRaw(CarOutline);
const SearchOutlineIcon = markRaw(SearchOutline);
const AddOutlineIcon = markRaw(AddOutline);

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  selectedOutboundOrg: {
    type: Object,
    default: null,
  },
});

// 定义组件事件
const emit = defineEmits([
  "show-vehicle-selector",
  "handle-outbound-org-change",
  "handle-sale-price-change",
  "handle-discount-change",
  "handle-sales-expense-change",
  "update:form",
  "validate",
  "vehicle-selected", // 添加车辆选择事件
]);

// 计算属性：表单数据的引用
const form = computed(() => props.form);

// 计算属性：成交价格（万元）
const finalPriceInWan = computed(() => {
  return convertToWanYuan(props.form.finalPrice);
});

// 组件状态
const showBizOrgSelector = ref(false);

// 处理日期变化
const handleDateChange = (field, value) => {
  const updatedForm = {
    ...props.form,
    [field]: value,
  };
  emit("update:form", updatedForm);
};

// 处理出库单位选择
const handleOutboundOrgSelect = (orgs) => {
  if (orgs && orgs.length > 0) {
    const selectedOrg = orgs[0]; // 因为是单选模式，所以取第一个

    // BizOrgSelector返回的字段是orgName，需要正确映射
    const updatedForm = {
      ...props.form,
      outboundOrgName: selectedOrg.orgName || selectedOrg.name || "",
      outboundOrgId: selectedOrg.id,
    };

    emit("update:form", updatedForm);
    emit("handle-outbound-org-change", selectedOrg);

    // 关闭选择器
    showBizOrgSelector.value = false;
  }
};

// 处理出库单位选择取消
const handleOutboundOrgCancel = () => {
  // 更新表单数据
  const updatedForm = {
    ...props.form,
    outboundOrgName: "",
    outboundOrgId: null,
  };

  emit("update:form", updatedForm);
  emit("handle-outbound-org-change", null);
  showBizOrgSelector.value = false;
};

// 处理销售价格变化
const handleSalePriceChange = () => {
  emit("handle-sale-price-change");
};

// 处理优惠金额变化
const handleDiscountChange = () => {
  emit("handle-discount-change");
};

// 处理销售费用变化
const handleSalesExpenseChange = () => {
  emit("handle-sales-expense-change");
};

// 处理出库单位点击
const handleOutboundOrgClick = () => {
  if (!props.form.vehicleBrand) {
    messages.warning("请先选择车辆品牌");
    return;
  }
  // 打开选择器前，先清空当前选择
  const updatedForm = {
    ...props.form,
    outboundOrgName: "",
    outboundOrgId: null,
  };
  emit("update:form", updatedForm);
  showBizOrgSelector.value = true;
};

// 处理车辆选择
const handleVehicleSelected = (vehicle) => {
  if (!vehicle) {
    return;
  }

  // 更新表单数据
  const updatedForm = {
    ...props.form, // 保留其他字段的值
    skuId: vehicle.id,
    vehicleBrand: vehicle.brand || "",
    vehicleSeries: vehicle.series || "",
    vehicleConfig: vehicle.configName || "",
    vehicleColorCode: vehicle.colorCode || "",
    vehicleSbPrice: vehicle.sbPrice || 0,
    vehicleSalePrice: parseFloat((vehicle.sbPrice * 1.35).toFixed(2)) || 0,
    // 清空出库单位相关数据
    outboundOrgId: null,
    outboundOrgName: "",
  };

  emit("update:form", updatedForm);
  emit("vehicle-selected", vehicle);
};

// 处理显示车辆选择器
const handleShowVehicleSelector = () => {
  emit("show-vehicle-selector");
};
</script>

<style lang="scss" scoped>
.outbound-org-selector {
  width: 100%;

  &.disabled {
    :deep(.n-input) {
      cursor: not-allowed;
      background-color: var(--n-disabled-color);
    }
  }

  :deep(.n-input) {
    width: 100%;
    cursor: pointer;
  }

  :deep(.n-button) {
    margin-right: -4px;

    &.n-button--disabled {
      cursor: not-allowed;
    }
  }

  .add-icon {
    color: var(--primary-color, #18a058);
    font-size: 18px;

    &.disabled {
      color: var(--n-disabled-color);
    }
  }
}
</style>
